import {
  CONTACT_CHANNEL_OPTIONS,
  FOLLOW_UP_STATUS_OPTIONS,
  OPPORTUNITY_OPTIONS,
  SERVICE_OPTIONS,
} from "@components/badge";
import type { FormField } from "@components/leadDrawer/interface";

export const leadProfileField = (t: (key: string) => string) => {
  const formFields: FormField[] = [
    {
      id: "followUpStatus",
      label: t("leadProfile.followUpStatus"),
      options: FOLLOW_UP_STATUS_OPTIONS,
      placeholder: t("leadProfile.followUpStatus"),
      type: "select",
    },
    {
      id: "opportunity",
      label: t("leadProfile.opportunity"),
      options: OPPORTUNITY_OPTIONS,
      placeholder: t("addLead.opportunity"),
      type: "select",
    },
    {
      id: "servicesOfInterest",
      label: t("leadProfile.servicesOfInterest"),
      options: SERVICE_OPTIONS,
      placeholder: t("addLead.servicesOfInterest"),
      type: "select",
    },
    {
      id: "contactChannel",
      label: t("leadProfile.contactChannel"),
      options: CONTACT_CHANNEL_OPTIONS,
      placeholder: t("leadProfile.contactChannel"),
      required: true,
      type: "select",
    },
    {
      disabled: true,
      id: "lastFollowUpDate",
      label: t("leadProfile.lastFollowUpDate"),
      placeholder: t("leadProfile.lastFollowUpDate"),
      type: "date",
    },
    {
      id: "followUpDate",
      label: t("leadProfile.followUpDate"),
      placeholder: t("leadProfile.followUpDate"),
      type: "date",
    },
    {
      disabled: true,
      id: "totalDayToNextFollowUp",
      label: t("leadProfile.totalDayToNextFollowUp"),
      placeholder: t("leadProfile.totalDayToNextFollowUp"),
      type: "input",
      variant: "transparent",
    },
    {
      id: "startDate",
      label: t("leadProfile.startDate"),
      placeholder: t("leadProfile.startDate"),
      type: "date",
    },
    {
      disabled: true,
      id: "totalDayFromStartDate",
      label: t("leadProfile.totalDayFromStartDate"),
      placeholder: t("leadProfile.totalDayFromStartDate"),
      type: "input",
      variant: "transparent",
    },
    {
      id: "contactInfo",
      label: t("leadProfile.contactInfo"),
      placeholder: t("leadProfile.contactInfo"),
      type: "input",
      variant: "transparent",
    },
  ];

  return formFields;
};
