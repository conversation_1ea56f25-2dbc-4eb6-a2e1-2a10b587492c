import { render, screen, fireEvent } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";
import "@testing-library/jest-dom/vitest";
import { Input } from "../../../src/components/common";

describe("Input Component", () => {
  it("should render input correctly", () => {
    render(<Input placeholder="Test Input" />);
    expect(screen.getByPlaceholderText("Test Input")).toBeInTheDocument();
    expect(screen.getByRole("textbox")).toBeInTheDocument();
  });

  it("should apply base classes correctly", () => {
    const { container } = render(<Input placeholder="Test Input" />);
    const wrapper = container.firstChild as HTMLElement;

    // Test wrapper (label) classes
    expect(wrapper).toHaveClass(
      "input",
      "input-primary",
      "h-8",
      "w-full",
      "rounded-lg"
    );

    // Test actual input element classes
    const input = screen.getByRole("textbox");
    expect(input).toHaveClass("h-fit", "w-full");
  });

  it("should render as input element by default", () => {
    render(<Input />);
    const input = screen.getByRole("textbox");
    expect(input.tagName).toBe("INPUT");
  });

  it("should apply correct classes for different variants", () => {
    const variants = ["default", "transparent"] as const;

    variants.forEach((variant) => {
      const { container, unmount } = render(
        <Input variant={variant} placeholder="Test Input" />
      );
      const wrapper = container.firstChild as HTMLElement;

      switch (variant) {
        case "default":
          expect(wrapper).toHaveClass("bg-base-200");
          break;
        case "transparent":
          expect(wrapper).toHaveClass("bg-transparent");
          expect(wrapper).toHaveClass("shadow-none");
          break;
      }

      unmount();
    });
  });

  it("should handle user input correctly", () => {
    render(<Input placeholder="Type here" />);
    const input = screen.getByRole("textbox");

    fireEvent.change(input, { target: { value: "Hello World" } });
    expect(input).toHaveValue("Hello World");
  });

  it("should handle controlled value", () => {
    const { rerender } = render(
      <Input value="Initial Value" onChange={() => {}} />
    );
    const input = screen.getByRole("textbox");

    expect(input).toHaveValue("Initial Value");

    rerender(<Input value="Updated Value" onChange={() => {}} />);
    expect(input).toHaveValue("Updated Value");
  });

  it("should render prefix icon", () => {
    render(
      <Input
        prefixIcon={<span data-testid="prefix-icon">📧</span>}
        placeholder="Email"
      />
    );

    expect(screen.getByTestId("prefix-icon")).toBeInTheDocument();
    expect(screen.getByText("📧")).toBeInTheDocument();
  });

  it("should render suffix icon", () => {
    render(
      <Input
        suffixIcon={<span data-testid="suffix-icon">🔍</span>}
        placeholder="Search"
      />
    );

    expect(screen.getByTestId("suffix-icon")).toBeInTheDocument();
    expect(screen.getByText("🔍")).toBeInTheDocument();
  });

  it("should apply active state correctly", () => {
    const { container } = render(<Input isActive placeholder="Active Input" />);
    const wrapper = container.firstChild as HTMLElement;

    expect(wrapper).toHaveClass("active");
  });

  it("should apply custom className", () => {
    const { container } = render(<Input className="custom-class" />);
    const wrapper = container.firstChild as HTMLElement;

    expect(wrapper).toHaveClass("custom-class");
    // Should still have base classes
    expect(wrapper).toHaveClass("input", "input-primary");
  });

  it("should handle disabled state", () => {
    render(<Input disabled placeholder="Disabled Input" />);
    const input = screen.getByRole("textbox");

    expect(input).toBeDisabled();
  });

  it("should pass through HTML input attributes", () => {
    render(
      <Input
        id="test-input"
        data-testid="custom-input"
        maxLength={10}
        placeholder="Test"
      />
    );

    const input = screen.getByRole("textbox");
    expect(input).toHaveAttribute("id", "test-input");
    expect(input).toHaveAttribute("data-testid", "custom-input");
    expect(input).toHaveAttribute("maxLength", "10");
  });

  // Button mode tests
  it("should render as button when isButton is true", () => {
    render(<Input isButton placeholder="Button Input" />);
    const button = screen.getByRole("button");

    expect(button.tagName).toBe("BUTTON");
    expect(button).toHaveAttribute("type", "button");
  });

  it("should handle click events when isButton is true", () => {
    const handleClick = vi.fn();
    render(<Input isButton onClick={handleClick} placeholder="Clickable" />);

    const button = screen.getByRole("button");
    fireEvent.click(button);

    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it("should display value and placeholder correctly in button mode", () => {
    const { rerender } = render(<Input isButton placeholder="Select option" />);

    // Should show placeholder when no value
    expect(screen.getByText("Select option")).toBeInTheDocument();

    rerender(
      <Input isButton value="Selected Value" placeholder="Select option" />
    );

    // Should show value when provided
    expect(screen.getByText("Selected Value")).toBeInTheDocument();
    expect(screen.queryByText("Select option")).not.toBeInTheDocument();
  });

  it("should render prefix and suffix icons in button mode", () => {
    render(
      <Input
        isButton
        prefixIcon={<span data-testid="btn-prefix">📋</span>}
        suffixIcon={<span data-testid="btn-suffix">▼</span>}
        placeholder="Dropdown"
      />
    );

    expect(screen.getByTestId("btn-prefix")).toBeInTheDocument();
    expect(screen.getByTestId("btn-suffix")).toBeInTheDocument();
  });

  it("should apply active state in button mode", () => {
    render(<Input isButton isActive placeholder="Active Button" />);
    const button = screen.getByRole("button");

    expect(button).toHaveClass("active");
  });

  it("should handle disabled state in button mode", () => {
    render(<Input isButton disabled placeholder="Disabled Button" />);
    const button = screen.getByRole("button");

    expect(button).toBeDisabled();
  });

  it("should apply custom className in button mode", () => {
    render(<Input isButton className="custom-button-class" />);
    const button = screen.getByRole("button");

    expect(button).toHaveClass("custom-button-class");
    // Should still have base button classes
    expect(button).toHaveClass("flex", "h-8", "w-full", "cursor-pointer");
  });

  it("should apply variant classes in button mode", () => {
    const { rerender } = render(
      <Input isButton variant="default" placeholder="Default Button" />
    );
    let button = screen.getByRole("button");
    expect(button).toHaveClass("bg-base-200");

    rerender(
      <Input isButton variant="transparent" placeholder="Transparent Button" />
    );
    button = screen.getByRole("button");
    expect(button).toHaveClass("bg-transparent");
  });
});
